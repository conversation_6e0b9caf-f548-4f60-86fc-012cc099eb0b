<script lang="ts">
	import List from '../generic/list/List.svelte';

	let {
		data,
		selected = $bindable()
	}: { data: { assetItem: string | null }[]; selected?: { id: string; title: string } } = $props();

	let id = 0;

	const set = new Set(data.map((a) => (a.assetItem || 'BLK').substring(0, 3)));
	const items = Array.from(set)
		.sort()
		.map((a) => ({ id: (++id).toString(), title: a }));
</script>

<List {items} bind:selected />
