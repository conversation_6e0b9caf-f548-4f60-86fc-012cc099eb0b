import { pgTable, pgSchema, varchar, integer } from "drizzle-orm/pg-core"
import { sql } from "drizzle-orm"

export const neldata = pgSchema("neldata");


export const locationHierarchyInNeldata = neldata.table("location_hierarchy", {
	assetItem: varchar("Asset Item", { length: 50 }),
	assetIdValidationCategory: varchar("Asset ID Validation Category", { length: 50 }),
	description: varchar("Description", { length: 5000 }),
	owner: varchar("Owner", { length: 50 }),
	subType: varchar("SubType", { length: 50 }),
	location: varchar("Location", { length: 5000 }),
	wbs: varchar("WBS", { length: 5000 }),
	wbsDescription: varchar("WBS Description", { length: 5000 }),
	controlLine: varchar("ControlLine", { length: 50 }),
	chainage: varchar("Chainage", { length: 50 }),
	column11: integer("Column11"),
	locationId: varchar("Location ID", { length: 50 }),
	locationDescription: varchar("Location Description", { length: 5000 }),
	chainage1: varchar("Chainage_1", { length: 50 }),
	controlLine1: varchar("ControlLine_1", { length: 50 }),
	plant: varchar("Plant", { length: 50 }),
	facility: varchar("Facility", { length: 50 }),
	area: varchar("Area", { length: 5000 }),
	location1: varchar("Location_1", { length: 5000 }),
	column20: varchar("Column20", { length: 50 }),
	column21: varchar("Column21", { length: 50 }),
});
