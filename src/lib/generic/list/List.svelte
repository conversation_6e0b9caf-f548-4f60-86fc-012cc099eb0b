<script lang="ts" module>
	export type ListItem = {
		id: string;
		title: string;
		selected?: boolean;
	};
</script>

<script lang="ts">
	let { items, selected = $bindable() }: { items: ListItem[]; selected?: ListItem } = $props();
</script>

<div class="relative">
	<ul class="list-none rounded-md p-2">
		{#each items as item (item.id)}
			<li class="cursor-pointer">
				<!-- svelte-ignore a11y_click_events_have_key_events -->
				<!-- svelte-ignore a11y_no_static_element_interactions -->
				<!-- svelte-ignore event_directive_deprecated -->
				<div
					class="ti relative"
					class:selected={item.selected}
					on:click={() => {
						if (selected) {
							selected.selected = false;
						}
						selected = item;
						selected.selected = true;
					}}
				>
					<span>{item.title}</span>
				</div>
			</li>
		{/each}
	</ul>
</div>

<style>
	.selected {
		background-color: #a46233;
		color: white;
		border-radius: 0.75rem;
	}
</style>
