export type TreeItem = {
	id: number;
	children?: TreeItem[];
};

export type Table = Row[];

export type Row = {
	[key: string]: string | number | boolean | null;
};

type CreateItem = (
	row: Row,
	/**
	 * The column number in the hierarchy. Corresponds to the index of the `hierarchyColumn` array.
	 */
	hierarchyColumnNum: number
) => TreeItem;

export type Sort<T> = (a: T, b: T) => number;

/**
 * Tree will have maps temporarily during creation.
 */
type ItemTemp = TreeItem & {
	childrenMap?: Map<string, ItemTemp>;
};

type TreeItemWithTitle = TreeItem & {
	title: string;
};

const sortFn = (a: TreeItemWithTitle, b: TreeItemWithTitle) => a.title.localeCompare(b.title);

export function buildCustomTreeFromTable<T extends TreeItem, U>(
	table: Table,
	hierarchyColumns: string[],
	createItem: CreateItem,
	sort: Sort<T>,
	calculateNodeValue: (t: T) => U,
	accumulateToParent: (p: T, childValue: U) => void
): T {
	const tree: T & ItemTemp = {} as T & ItemTemp;
	table.forEach((row) => {
		processRow(row, hierarchyColumns, createItem, tree);
	});

	convertMapToArray(tree, sort, calculateNodeValue, accumulateToParent);

	return tree;
}

function processRow(row: Row, hierarchyColumns: string[], createItem: CreateItem, t: TreeItem) {
	let parent = t as ItemTemp;
	for (let i = 0; i < hierarchyColumns.length; i++) {
		const col = hierarchyColumns[i];
		const colValue = row[col]?.toString() || 'Blank';
		const r = parent.childrenMap?.get(colValue);
		if (r) {
			parent = r;
		} else {
			const newObj = createItem(row, i);
			if (!parent.childrenMap) {
				parent.childrenMap = new Map<string, ItemTemp>();
			}
			parent.childrenMap?.set(colValue, newObj);
			parent = newObj;
		}
	}
}

function calculateNodeValue(t) {
	return t.icon === 'asset' ? 1 : t.assetCount;
}

function accumulateToParent(p, childValue) {
	p.assetCount += childValue;
}

/**
 * - Converts the childrenMap to children array
 * - Accumulates the child values to the parent
 * - Sorts the children array
 * - Calls itself recursively for each child
 */
function convertMapToArray<T extends TreeItem, U>(
	t: T & ItemTemp,
	sortFn: Sort<T>,
	calculateNodeValue: (t: T) => U,
	accumulateToParent: (p: T, childValue: U) => void
) {
	if (t.childrenMap?.size) {
		t.children = Array.from(t.childrenMap?.values() || []);
		(t.children as T[]).sort(sortFn);
		t.children.forEach((a) => {
			const childValue = convertMapToArray(a as T, sortFn, calculateNodeValue, accumulateToParent);
			accumulateToParent(t, childValue);
		});
	}
	return calculateNodeValue(t);
}
