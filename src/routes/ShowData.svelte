<script lang="ts">
	import AssetTypeList from '$lib/nel/AssetTypeList.svelte';
	import { buildTreeFromTable } from '$lib/generic/tree/treeFromTable';
	import { getData } from './data.remote';
	import MyTreeView from './MyTreeView.svelte';
	import TreeView, { type Item } from './TreeView.svelte';

	let dataSet = await getData();

	let serial: number = 1;

	// add a map of all children to Item type
	type ItemMaster = Item & {
		allChildren?: Map<string, ItemMaster>;
		childrenCount: number;
		assetCount: number;
	};

	let tree = {
		children: [],
		allChildren: new Map<string, ItemMaster>(),
		childrenCount: 0,
		assetCount: 0
	} as {
		children?: Item[];
		allChildren?: Map<string, ItemMaster>;
		childrenCount: number;
		assetCount: number;
	};

	const createItem = (row: (typeof dataSet)[0], column: keyof (typeof dataSet)[0]) => {
		return {
			id: (++serial).toString(),
			title: row[column]?.toString() || 'Blank',
			icon: column as string,
			allChildren: undefined,
			childrenCount: 0,
			assetCount: 0
		};
	};

	const createItemLoc = (row: (typeof dataSet)[0], column: keyof (typeof dataSet)[0]) => {
		return {
			id: (++serial).toString(),
			title: row[column]?.toString() || 'Blank',
			icon: 'location',
			allChildren: undefined,
			childrenCount: 0,
			assetCount: 0
		};
	};

	const createItemAsset = (row: (typeof dataSet)[0], column: keyof (typeof dataSet)[0]) => {
		return {
			id: (++serial).toString(),
			title: row[column] ? row[column].toString() + ' - ' + row['description'] : 'Blank',
			icon: 'asset',
			allChildren: undefined,
			childrenCount: 0,
			assetCount: 0
		};
	};

	let hierarchy_columns: {
		column: keyof (typeof dataSet)[0];
		icon: string;
		createItem: typeof createItem;
	}[] = [
		{ column: 'plant', icon: 'plant', createItem },
		{ column: 'facility', icon: 'facility', createItem },
		{ column: 'area', icon: 'area', createItem },
		{ column: 'location1', icon: 'location', createItem: createItemLoc },
		{ column: 'assetItem', icon: 'asset', createItem: createItemAsset }
	];

	// dataSet.forEach((a) => {
	// 	processRow(a, tree);
	// });

	// function processRow(data: (typeof dataSet)[0], t: typeof tree) {
	// 	let parent: { allChildren?: Map<string, ItemMaster> } = t;
	// 	for (let i = 0; i < hierarchy_columns.length; i++) {
	// 		const col = hierarchy_columns[i].column;
	// 		const colValue = data[col]?.toString() || 'Blank';
	// 		const createItem = hierarchy_columns[i].createItem;
	// 		const r = parent.allChildren?.get(colValue);
	// 		if (r) {
	// 			parent = r;
	// 		} else {
	// 			const newObj = createItem(data, col);
	// 			if (!parent.allChildren) {
	// 				parent.allChildren = new Map<string, ItemMaster>();
	// 			}
	// 			parent.allChildren?.set(colValue, newObj);
	// 			parent = newObj;
	// 		}
	// 	}
	// }

	// function buildTree(t: typeof tree & Partial<ItemMaster>) {
	// 	t.children = Array.from(t.allChildren?.values() || []).sort((a, b) =>
	// 		a.title.localeCompare(b.title)
	// 	);
	// 	t.childrenCount = t.children?.length || 0;
	// 	t.allChildren?.forEach((a) => {
	// 		t.assetCount += buildTree(a);
	// 	});
	// 	return t.icon === 'asset' ? 1 : t.assetCount;
	// }

	// buildTree(tree);

	tree = buildTreeFromTable(
		dataSet,
		hierarchy_columns.map((a) => a.column)
	);
	let selectedAssetType: { id: string; title: string } | undefined = $state(undefined);
</script>

<div class="flex gap-4">
	<!-- <TreeView items={tree.children!} /> -->
	{JSON.stringify(selectedAssetType)}
	<div class="w-2/3">
		<MyTreeView treeData={tree} />
	</div>
	<div>
		<AssetTypeList data={dataSet} bind:selected={selectedAssetType} />
	</div>
</div>
