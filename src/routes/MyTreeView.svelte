<script module lang="ts">
	export type Item = {
		id: string;
		title: string;
		icon?: string;
		children?: Iterable<Item>;
		expanded?: boolean;
		selected?: boolean;
	};

	export type Tree = {
		children?: Iterable<Item>;
		selectedItem?: Item;
	};
</script>

<script lang="ts">
	const { treeData }: { treeData: Tree } = $props();
	const treeState = $state(treeData);
</script>

{#snippet treeItems(children: Iterable<Item>, tree: Tree, depth = 0)}
	<ul class="relative">
		{#if depth > 0}
			<div
				class="absolute top-2 bottom-2 w-px bg-gray-200 dark:bg-gray-700"
				style="left: {0.5 + depth * 1}rem"
			></div>
		{/if}
		{#each children as item (item.id)}
			<li class="cursor-pointer">
				<div style="padding-left: {(depth + 1) * 1}rem">
					<!-- svelte-ignore a11y_click_events_have_key_events -->
					<!-- svelte-ignore a11y_no_noninteractive_element_interactions -->
					<!-- svelte-ignore event_directive_deprecated -->
					<!-- svelte-ignore a11y_no_static_element_interactions -->
					<div
						class="ti relative"
						class:selected={item.selected}
						on:click={() => {
							if (tree.selectedItem) {
								tree.selectedItem.selected = false;
							}
							tree.selectedItem = item;
							item.selected = true;
							item.expanded = !item.expanded;
						}}
					>
						<img src="/images/icons/{item.icon}.svg" alt={item.icon} class="h-4 w-4" />
						<span>{item.title}</span>
						<span
							class="me-2 ml-auto rounded-sm bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-300"
							>{item.childrenCount ? item.childrenCount : ''}</span
						>
						{#if item.childrenCount}
							<span>∣</span>
							<span
								class="me-2 rounded-sm bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800 dark:bg-gray-700 dark:text-gray-300"
								>{item.assetCount ? item.assetCount : ''}</span
							>
						{/if}
					</div>
				</div>
				{#if item.children && item.expanded}
					{@render treeItems(item.children, tree, depth + 1)}
				{/if}
			</li>
		{/each}
	</ul>
{/snippet}

<div
	class="not-content relative grid min-h-[500px] place-items-center overflow-clip rounded-2xl border
	bg-gray-100 text-gray-700 dark:border-gray-700 dark:bg-gray-950 dark:text-gray-400"
>
	<div class="w-full min-w-0 overflow-clip p-2">
		<ul class="list-none rounded-md p-2">
			{@render treeItems(treeState.children || [], treeState)}
		</ul>
	</div>
</div>

<style>
	.ti {
		display: flex;
		gap: 0.5rem;
		padding: 0.5rem;
		align-items: center;
	}

	.selected {
		background-color: #a46233;
		color: white;
		border-radius: 0.75rem;
	}
</style>
